from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict, PlainSerializer
from typing import Optional, Any, Annotated, Dict
from datetime import datetime
from bson import ObjectId
from pydantic import HttpUrl


class ProjectBase(BaseModel):
    name: str
    description: Optional[str] = None
    webhook_callback: Optional[HttpUrl] = Field(None, description="URL to call when job is completed")

class ProjectCreate(ProjectBase):
    pass

class ProjectUpdate(ProjectBase):
    name: Optional[str] = None
    description: Optional[str] = None
    updated_at: Optional[datetime] = None

    @field_validator("name")
    def validate_name(cls, value):
        if value is not None and len(value) < 3:
            raise ValueError("Project name must be at least 3 characters long")
        return value
    
    @field_validator("description")
    def validate_description(cls, value):
        if value is not None and len(value) > 500:
            raise ValueError("Project description must be less than 500 characters")
        return value
    
   
    @field_validator("updated_at", mode="before")
    def add_default_updated_at(cls, value):
        if value is None:
            return datetime.now()
        return value 


    @model_validator(mode="after")
    def check_at_least_one_field(cls, values):
        if values.name is None and values.description is None:
            raise ValueError("At least one field (name or description) must be provided")
        return values
    
class JobStatistics(BaseModel):
    """Job statistics for a project"""
    by_status: Dict[str, int] = Field(default_factory=dict, description="Job counts by status")
    by_process_type: Dict[str, int] = Field(default_factory=dict, description="Job counts by process type")
    total_jobs: int = Field(default=0, description="Total number of jobs in the project")

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )


class Project(ProjectBase):
    id: Annotated[ObjectId,PlainSerializer(str)] = Field(alias="_id")
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: Annotated[ObjectId,PlainSerializer(str)]
    created_by_username: Optional[str] = None

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )


class ProjectWithStats(ProjectBase):
    """Project model with job statistics"""
    id: Annotated[ObjectId,PlainSerializer(str)] = Field(alias="_id")
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: Annotated[ObjectId,PlainSerializer(str)]
    job_statistics: JobStatistics = Field(default_factory=JobStatistics, description="Job statistics for this project")

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )